时间段异常检测方法说明 V6.0 (简化版)
===========================================

## 系统概述
本系统实现基于时间段的简化运行状态识别和异常检测，将复杂的多状态识别简化为3种基本状态，
并完全停用机器学习方法，仅使用统计方法进行异常检测。

## 1. 时间段划分方法
### 分割点检测算法
- 使用滑动窗口（24小时）检测变异系数变化
- 三重条件检测：变异系数差异 > 0.8，均值差异 > 0.5，中位数差异 > 0.5
- 最小间隔：12小时（避免过度分割）
- 最大时间段数：8个（7个分割点）

### 时间段标识
- 在散点图中用绿色虚线标识时间段分割点
- 每个时间段添加状态标识和置信度信息

## 2. 简化运行状态识别（3种状态）
### 停运状态
- 判定条件：零值或极低值（≤0.2）比例 ≥ 70%
- 数据特征：长期稳定在零值或极低值范围
- 置信度计算：0.5 + 零值比例（最高0.95）

### 单状态运行
- 判定条件：零值比例 < 70% 且 变异系数 < 0.3
- 数据特征：围绕某个中心值相对稳定波动
- 置信度计算：0.6 + (0.3 - 变异系数)（最高0.95）

### 正常波动
- 判定条件：零值比例 < 70% 且 变异系数 ≥ 0.3
- 数据特征：存在明显的规律性或随机性变化
- 置信度计算：0.5 + min(变异系数, 1.0)（最高0.95）

## 3. 简化异常检测方法
### 停用的方法
- ❌ LOF局部异常因子检测（完全停用）
- ❌ DBSCAN聚类检测（完全停用）
- ❌ 机器学习层检测（完全停用）

### 保留的统计方法（等权重）
#### P5/P95百分位数阈值法
- 计算正值数据的5%和95%分位数
- 异常判定：值 < P5 或 值 > P95
- 可信度评分：3分

#### IQR四分位距法
- 计算：Q1-1.5×IQR 和 Q3+1.5×IQR
- 异常判定：值在IQR范围外
- 可信度评分：3分

#### MAD中位数绝对偏差法
- 计算：中位数 ± 2.5×MAD
- 异常判定：值在MAD范围外
- 可信度评分：3分

### 业务规则层
- 负值异常：所有负值自动判定为异常
- 可信度评分：5分（最高优先级）

## 4. 区间保护机制
### 保护区间计算
- 基准值：该时间段内数据的中位数
- 调整系数：根据标准差反向调整
  * 标准差 < 0.1：调整系数 = 1.5（扩大保护区间）
  * 标准差 0.1-0.5：调整系数 = 1.0（正常保护区间）
  * 标准差 > 0.5：调整系数 = 0.5（缩小保护区间）
- 保护区间 = 中位数 ± (标准差 × 调整系数)

### 保护规则
- 落在保护区间内的正值不判定为异常
- 保护区间不包含负值（负值仍按业务规则判定为异常）
- 仅适用于正值数据的保护

## 5. 四色标记系统
- 🔵 蓝色圆点：正常值（通过所有检测）
- 🟡 黄色圆点：统计异常值（被统计方法检测出）
- 🟡 黄色叉号：负值异常（违反物理规律）
- 🔴 红色圆点：明显异常值（综合评分≥6分的高置信度异常）
- 🟢 绿色圆点：零值（特殊标记）

## 6. 系统优势
- 简化运行状态识别，提高识别准确性
- 停用复杂机器学习方法，降低误报率
- 基于时间段的独立分析，更符合实际运行模式
- 区间保护机制减少正常波动的误判
- 等权重统计方法，避免方法偏向性

## 7. 技术参数
- 时间段检测窗口：24小时
- 变异系数阈值：0.8
- 固定阈值：0.5
- 最小时间段间隔：12小时
- 最大时间段数：8个
- 停运状态阈值：70%零值比例
- 单状态运行阈值：变异系数<0.3
- 保护区间调整系数：0.5-1.5

生成时间：2025-07-31 17:13:50
系统版本：V6.0 简化版
